<template>
  <div class="goods-card">
    <div class="goods-image">
      <img :src="goodsInfo.image" :alt="goodsInfo.name" loading="lazy" decoding="async" />
    </div>

    <div class="goods-info">
      <div class="goods-name">{{ goodsInfo.name }}</div>
      <div class="goods-details">
        <span class="goods-price">{{ goodsInfo.price }}</span>
        <span class="goods-sales" v-if="goodsInfo.sales > 0">销量: {{ goodsInfo.sales }}</span>
      </div>
      <div class="goods-spec" v-if="goodsInfo.spec">
        {{ goodsInfo.spec }}
      </div>
    </div>
  </div>
</template>

<script setup>
// 定义组件props
const props = defineProps({
  goodsInfo: {
    type: Object,
    required: true,
    default: () => ({
      image: '',
      name: '',
      price: 0,
      sales: 0,
      spec: '',
    })
  }
})
</script>

<style scoped lang="less">
.goods-card {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #f0f0f0;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;

  &:hover {
    border-color: #ff6600;
    box-shadow: 0 2px 8px rgba(255, 102, 0, 0.15);
  }
}

.goods-image {
  position: relative;
  width: 100%;
  height: 180px;
  overflow: hidden;
  background: #fafafa;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    background-color: #f5f5f5;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.goods-info {
  padding: 12px;
  background: #fff;

  .goods-name {
    font-size: 14px;
    font-weight: 400;
    color: #333;
    margin: 0 0 8px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-decoration: none;

    &:hover {
      color: #ff6600;
    }
  }

  .goods-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;

    .goods-price {
      color: #ff6600;
      font-size: 16px;
      font-weight: 700;
      line-height: 1;

      &::before {
        content: '¥';
        font-size: 12px;
        font-weight: 400;
        margin-right: 1px;
      }
    }

    .goods-sales {
      color: #999;
      font-size: 12px;
      line-height: 1;
      white-space: nowrap;
    }
  }

  .goods-spec {
    color: #666;
    font-size: 12px;
    line-height: 1.3;
    background: #f5f5f5;
    padding: 4px 8px;
    border-radius: 4px;
    margin-top: 4px;
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

// 添加商品卡片的响应式布局支持
.goods-card {
  width: 100%;
  max-width: 220px;
  min-width: 135px;
}
</style>
