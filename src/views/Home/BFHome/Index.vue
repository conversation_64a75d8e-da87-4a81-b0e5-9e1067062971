<template>
  <div class="fp-page">
    <SearchBox/>
    <header class="header" v-if="headerBannerList.length>0">
      <Swiper
        class="swiper"
        v-if="headerBannerList.length > 0"
        :data="headerBannerList"
        @slideClick="onSlideClick"
        @slideChange="onSwipeChange"
      >
        <template v-slot:slide="slotProps">
          <img :src="slotProps.item.imgUrl" :alt="slotProps.item.imgUrl" />
        </template>
        <template v-slot:pagination>
          <div class="swiper__indicator">{{ swipeCurIndex + 1 }}/{{ headerBannerList.length }}</div>
        </template>
      </Swiper>
    </header>
    <div class="icon-nav-container" v-if="iconList.length>0">
      <E2Scrollbar width="42px" v-if="iconList.length>5">
        <div class="icon-nav" :style="'width:'+(screenSize === 'type3' ? 15 : 20) * (iconList.length>5?iconList.length:5)+'%'">
          <div
            class="icon-item"
            v-for="item in iconList"
            :key="item.id"
            @click="onIconNavClick(item)">
            <img class="icon-item-img" :src="item.imgUrl" :alt="item.chName" data-name="fupin-index-1"/>
            <p class="icon-item-text">{{ item.chName }}</p>
          </div>
        </div>
      </E2Scrollbar>

      <!-- 九宫格 -->
      <div class="icon-nav" v-if="iconList.length<=5">
        <div
          class="icon-item"
          v-for="item in iconList"
          :key="item.id"
          @click="onIconNavClick(item)">
          <img class="icon-item-img" :src="item.imgUrl" :alt="item.chName" data-name="fupin-index-2"/>
          <p class="icon-item-text">{{ item.chName }}</p>
        </div>
      </div>
    </div>
    <div class="block-div">
      <Block title="各县销冠"
        v-if="limitedBannerList.length>0||limitedList.length>0"
        @click="onBlockClick(1)">
        <Swiper
          v-if="limitedBannerList && limitedBannerList.length > 0"
          class="swiper-com"
          :data="limitedBannerList"
          @slideClick="onSlideClickLimitedBannerList"
          @slideChange="onSwipeChangeLimitedBannerList"
        >
          <template v-slot:slide="slotProps">
            <img :src="slotProps.item.actImgUrl" :alt="slotProps.item.actImgUrl">
          </template>
          <template v-slot:pagination>
            <div class="custom-indicator">
              <span v-for="(_, index) in limitedBannerList" class="my-dot" :key="index" :class="{active: limitedBannerListIndex === index}"></span>
            </div>
          </template>
        </Swiper>
        <div class="limited-list wrap" v-if="limitedList.length>0" ref="waterfall">
          <FpGoods
            :style="{width:screenSize === 'type3' ? `${itemWidth}px` : ''}"
            v-for="item in limitedList"
            :key="item.id"
            :img="item.listImageUrl"
            :title="item.skuList[0]&&item.skuList[0].name?item.skuList[0].name:''"
            :saleVolume="item.skuList[0]&&item.skuList[0].realSaleVolume?item.skuList[0].realSaleVolume:''"
            :desc="specs(item)"
            :amt="priceCompute(item)[0]"
            @click="onGoodsClick(item)"
          />
          <div class="limited-list-cop" :style="{width:screenSize === 'type3' ? `${itemWidth}px` : ''}" v-for="(item,index) in limitedListCompensate"
               :key="index + item.id">
          </div>
        </div>
      </Block>
<!--      <Block title="推荐商品" v-if="recommendList.length>0" @click="onBlockClick(2)">-->
<!--        <div class="recommend wrap">-->
<!--          <div class="recommend-title">-->
<!--            <div class="recommend-title-inner">-->
<!--              <p class="recommend-text">-->
<!--                <span class="recommend-text1"></span>-->
<!--                <span class="recommend-text2">惊喜价</span>-->
<!--                <span class="recommend-text3">爆款单品&nbsp;&nbsp;限时折扣</span>-->
<!--              </p>-->
<!--            </div>-->
<!--          </div>-->
<!--          <div class="recommend-list" v-lazy-container="{ selector: 'img' }">-->
<!--            <div class="left" v-if="recommendList[0]" @click="onGoodsClick(recommendList[0])">-->
<!--              <div class="goods">-->
<!--                <img-->
<!--                  :data-src="recommendList[0].listImageUrl"-->
<!--                  :alt="recommendList[0].skuList[0]&&recommendList[0].skuList[0].name?recommendList[0].skuList[0].name:''"-->
<!--                  data-name="fp-index-4">-->
<!--                <div class="text">-->
<!--                  <h3 class="title">-->
<!--                    {{-->
<!--                      recommendList[0].skuList[0] && recommendList[0].skuList[0].name-->
<!--                        ? recommendList[0].skuList[0].name-->
<!--                        : ''-->
<!--                    }}-->
<!--                  </h3>-->
<!--                </div>-->
<!--                <div class="amt">-->
<!--                  <p class="amt1">-->
<!--                    <span>￥</span>-->
<!--                    <span class="big">{{ splitAmt(priceCompute(recommendList[0])[0])[0] }}</span>-->
<!--                    <span>.{{ splitAmt(priceCompute(recommendList[0])[0])[1] }}</span>-->
<!--                  </p>-->
<!--                </div>-->
<!--              </div>-->
<!--            </div>-->
<!--            <div class="right">-->
<!--              <div class="goodsmax" v-if="recommendList[1]" @click="onGoodsClick(recommendList[1])">-->
<!--                <div class="content">-->
<!--                  <div class="text">-->
<!--                    <h3 class="title">-->
<!--                      {{-->
<!--                        recommendList[1].skuList[0] && recommendList[1].skuList[0].name-->
<!--                          ? recommendList[1].skuList[0].name-->
<!--                          : ''-->
<!--                      }}-->
<!--                    </h3>-->
<!--                  </div>-->
<!--                  <div class="amt">-->
<!--                    <p class="amt1">-->
<!--                      <span>￥</span>-->
<!--                      <span class="big">{{ splitAmt(priceCompute(recommendList[1])[0])[0] }}</span>-->
<!--                      <span>.{{ splitAmt(priceCompute(recommendList[1])[0])[1] }}</span>-->
<!--                    </p>-->
<!--                  </div>-->
<!--                </div>-->
<!--                <img-->
<!--                  :data-src="recommendList[1].listImageUrl"-->
<!--                  :alt="recommendList[1].skuList[0]&&recommendList[1].skuList[0].name?recommendList[1].skuList[0].name:''"-->
<!--                  data-name="fp-index-5">-->
<!--              </div>-->
<!--              <div class="goodsmax" v-if="recommendList[2]" @click="onGoodsClick(recommendList[2])">-->
<!--                <div class="content">-->
<!--                  <div class="text">-->
<!--                    <h3 class="title">-->
<!--                      {{-->
<!--                        recommendList[2].skuList[0] && recommendList[2].skuList[0].name-->
<!--                          ? recommendList[2].skuList[0].name-->
<!--                          : ''-->
<!--                      }}-->
<!--                    </h3>-->
<!--                  </div>-->
<!--                  <div class="amt">-->
<!--                    <p class="amt1">-->
<!--                      <span>￥</span>-->
<!--                      <span class="big">{{ splitAmt(priceCompute(recommendList[2])[0])[0] }}</span>-->
<!--                      <span>.{{ splitAmt(priceCompute(recommendList[2])[0])[1] }}</span>-->
<!--                    </p>-->
<!--                  </div>-->
<!--                </div>-->
<!--                <img-->
<!--                  :data-src="recommendList[2].listImageUrl"-->
<!--                  :alt="recommendList[2].skuList[0]&&recommendList[2].skuList[0].name?recommendList[2].skuList[0].name:''"-->
<!--                  data-name="fp-index-6">-->
<!--              </div>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </Block>-->
      <Block title="新上好物" v-if="newerList.length>0" @click="onBlockClick(3)">
        <div class="newer-list" ref="newerScrollRef">
          <div class="newer-list-inner wrap" :style="`width:${goodsWidth*newerList.length+5*newerList.length-1}px`">
            <Goods
              v-for="(item,index) in newerList"
              :key="item.id"
              :style="`width:${goodsWidth}px;${index===0?'':'margin-left: 5px'}`"
              :img="item.listImageUrl"
              :title="item.skuList[0]&&item.skuList[0].name?item.skuList[0].name:''"
              :desc="specs(item)"
              :amt="priceCompute(item)[0]"
              descColorful
              @click="onGoodsClick(item)"/>
          </div>
        </div>
      </Block>
      <Block title="爆款好物" v-if="guessList.length>0">
        <div class="guess-list" ref="waterfall">
          <FpGoods
            :style="{width:screenSize === 'type3' ? `${itemWidth}px` : ''}"
            v-for="item in guessList" :key="item.id"
            :img="item.listImageUrl"
            :title="item.skuList[0]&&item.skuList[0].name?item.skuList[0].name:''"
            :desc="specs(item)"
            :amt="priceCompute(item)[0]"
            :saleVolume="item.skuList[0]&&item.skuList[0].realSaleVolume?item.skuList[0].realSaleVolume:''"
            @click="onGoodsClick(item)"
          />
        </div>
        <div class="limited-list-cop" :style="{width:screenSize === 'type3' ? `${itemWidth}px` : ''}" v-for="(item,index) in guessListCompensate"
             :key="index + item.id">
        </div>
      </Block>
    </div>

    <FloatTag :bottom="150" :zIndex="9999"/>
    <ActivityPopup/>
  </div>
</template>
<script>
import E2Scrollbar from './components/E2Scrollbar.vue'
import { Swiper } from 'wo-e2'
import { isWopay, isUnicom } from 'commonkit'
import Goods from './components/Goods.vue'
import FloatTag from '@/components/FloatTag'
import SearchBox from '@/components/SearchBox'
import FpGoods from './components/FPGoods.vue'
import Block from '@/components/Common/Home/Block.vue'
import { getBannerInfo, getIconInfo } from '@/api/bannerIcon'
import { getGoodsList, getGoodsDetail } from '@/api/goods'
import { splitAmt, fenToYuan, priceCompute } from '@/utils/amount'
import { jumpHandler } from '@/utils/jump'
import { curChannelBiz, getBizCode } from '@/utils/curEnv'
import envConfig from '@/env.config'
import ActivityPopup from '@/components/ActivityPopup'

// 页面宽度
const clientWidth = document.body.clientWidth
// 商品宽度
// const goodsWidth = (clientWidth - 44) / 3.5
// icon 宽度
const iconWidth = (clientWidth - 44) / 5

// banner 过滤对应渠道的数据
const channelFilterd = list => {
  if (isUnicom) {
    return list.filter(item => item.channelType === '1')
  } else if (isWopay) {
    return list.filter(item => item.channelType === '0')
  } else {
    return list.filter(item => item.channelType === '2')
  }
}

export default {
  components: { Swiper, E2Scrollbar, Block, Goods, FpGoods, SearchBox, FloatTag, ActivityPopup },
  data () {
    return {
      fpGoodsStyleObj: {
        width: `${this.itemWidth}px}`
      },
      swipeCurIndex: 0,
      limitedBannerListIndex: 0,
      headerSwiperOptions: {
        autoplay: {
          delay: 5000,
          stopOnLastSlide: false,
          disableOnInteraction: false
        },
        loop: true,
        pagination: {
          el: '.header-swiper-pagination',
          type: 'fraction'
        },
        on: {
          tap: (event) => {
            const bannerId = event.target.dataset.id
            const item = this.headerBannerList.filter(item => item.bannerId === bannerId)[0]
            if (item) this.onBannerClick(item)
          }
        }
      },
      limitedSwiperOptions: {
        autoplay: {
          delay: 5000,
          stopOnLastSlide: false,
          disableOnInteraction: false
        },
        loop: true,
        pagination: {
          el: '.limited-swiper-pagination',
          type: 'bullets'
        },
        on: {
          tap: (event) => {
            const bannerId = event.target.dataset.id
            const item = this.limitedBannerList.filter(item => item.bannerId === bannerId)[0]
            if (item) this.onBannerClick(item)
          }
        }
      },
      headerBannerList: [], // 顶部banner数据
      iconList: [], // icon导航区数据
      limitedBannerList: [], // 限量专区banner数据
      limitedList: [], // 限量商品
      limitedListCompensate: [],
      recommendList: [], // 推荐商品
      newerList: [], // 新上好物商品
      guessList: [], // 新上好物商品
      guessListCompensate: [],
      goodsWidth: 0, // 新上好物商品宽度
      iconWidth, // icon宽度
      newerScrollLeft: 0,
      screenWidth: window.innerWidth,
      screenSize: '',
      col: 2,
      itemWidth: 0,
      gutterWidth: 0
    }
  },
  computed: {
    isLogin () {
      return this.$store.state.user.isLogin
    },
    cifUserId () {
      return this.$store.state.user.cifUserId
    },
    curAddrInfo () {
      return this.$store.getters['user/curAddressInfo']
    }
  },
  methods: {
    // 商品售价和划线价处理
    priceCompute,
    setCol () {
      if (window.innerWidth > 540 && window.innerWidth < 768) {
        this.col = 4
        return
      }
      if (window.innerWidth >= 768) {
        this.col = 5
        return
      }
      this.col = 2
      console.warn('this.col', this.col)
    },
    setItemWidth () {
      const element = this.$refs.waterfall
      this.goodsWidth = window.innerWidth / (this.col + 1)
      if (element) {
        console.warn('element.offsetWidth', element.offsetWidth)
        this.itemWidth = element.offsetWidth / this.col - (10 + this.col)
        console.warn('this.itemWidth', this.itemWidth)
        return
      }
      this.itemWidth = window.innerWidth / this.col - this.gutterWidth
    },
    setGutterWidth () {
      this.gutterWidth = 10 + this.col - 1
    },
    getLimitedListCompensate () {
      if (this.limitedList % this.col !== 0) {
        const remainder = parseInt(this.limitedList.length / this.col)
        if (remainder * this.col < this.limitedList.length) {
          const computer = this.limitedList.length - remainder * this.col
          this.limitedListCompensate = this.limitedList.slice(0, this.col - computer)
        } else {
          const computer = remainder * this.col - this.limitedList.length
          this.limitedListCompensate = this.limitedList.slice(0, this.col - computer)
        }
      }
    },
    getGuessListCompensate () {
      if (this.guessList % this.col !== 0) {
        const remainder = parseInt(this.guessList.length / this.col)
        if (remainder * this.col < this.guessList.length) {
          const computer = this.guessList.length - remainder * this.col
          this.guessListCompensate = this.guessList.slice(0, this.col - computer)
        } else {
          const computer = remainder * this.col - this.guessList.length
          this.guessListCompensate = this.guessList.slice(0, this.col - computer)
        }
      }
    },
    determineScreenSize () {
      let size
      if (this.screenWidth < 320) {
        size = 'type1'
      } else if (this.screenWidth >= 320 && this.screenWidth <= 540) {
        size = 'type2'
      } else {
        size = 'type3'
      }
      this.screenSize = size
    },
    onResize () {
      this.setCol()
      this.setGutterWidth()
      this.setItemWidth()
      this.screenWidth = window.innerWidth
      this.determineScreenSize()
      this.getLimitedListCompensate()
      this.getGuessListCompensate()
    },

    onSlideClick (index, item, $swiper) {
      jumpHandler(item.url)
    },
    onSlideClickLimitedBannerList (index, item, $swiper) {
      jumpHandler(item.url)
    },
    onSwipeChange (index) {
      this.swipeCurIndex = index
    },
    onSwipeChangeLimitedBannerList (index) {
      this.limitedBannerListIndex = index
    },
    // 顶部banner列表
    async getHeaderBannerList () {
      this.$toast.queueLoading()
      const [err, json] = await getBannerInfo({ bizCode: getBizCode('QUERY'), showPage: 1 })
      this.$toast.queueClear()
      if (!err) this.headerBannerList = channelFilterd(json)
    },
    // 限量专区banner列表
    async getLimitedBannerList () {
      this.$toast.queueLoading()
      const [err, json] = await getBannerInfo({ bizCode: getBizCode('QUERY'), showPage: 2 })
      this.$toast.queueClear()
      if (!err) this.limitedBannerList = channelFilterd(json)
    },
    // icon列表
    async getIconList () {
      this.$toast.queueLoading()
      const [err, json] = await getIconInfo({ bizCode: getBizCode('QUERY'), channel: curChannelBiz.get(), showPage: 2 })
      this.$toast.queueClear()
      if (!err) {
        if (json) {
          this.iconList = json.slice(0, 5)
        } else {
          this.iconList = []
        }
      }
    },
    // 限量专区商品列表
    async getLimitedList () {
      this.$toast.queueLoading()
      const [err, json] = await getGoodsList({
        type: 'partion',
        bizCode: getBizCode('GOODS'),
        page_no: 1,
        page_size: 6,
        id: envConfig.FP_HOME_PAGE_LIMITED_GOODS_ID
      })
      this.$toast.queueClear()
      if (!err) {
        this.limitedList = json
        this.getLimitedListCompensate()
      }
    },
    // 推荐商品列表
    async getRecommendList () {
      this.$toast.queueLoading()
      const [err, json] = await getGoodsList({
        type: 'partion',
        bizCode: getBizCode('GOODS'),
        page_no: 1,
        page_size: 3,
        id: envConfig.FP_HOME_PAGE_RECOMMEND_GOODS_ID
      })
      this.$toast.queueClear()
      if (!err) this.recommendList = json
    },
    // 新上好物商品列表
    async getNewerList () {
      this.$toast.queueLoading()
      const [err, json] = await getGoodsList({
        type: 'partion',
        bizCode: getBizCode('GOODS'),
        page_no: 1,
        page_size: 10,
        id: envConfig.FP_HOME_PAGE_NEWER_GOODS_ID
      })
      this.$toast.queueClear()
      if (!err) this.newerList = json
      if (this.newerList.length > 0) {
        this.$nextTick(() => {
          this.$refs.newerScrollRef.addEventListener('scroll', this.newerScrollEvent, { passive: true })
        })
      }
    },
    // 猜你喜欢商品列表
    async getGuessList () {
      this.$toast.queueLoading()
      const [err, json] = await getGoodsList({
        type: 'partion',
        bizCode: getBizCode('GOODS'),
        page_no: 1,
        page_size: 100,
        id: envConfig.FP_HOME_PAGE_GUESS_GOODS_ID
      })
      this.$toast.queueClear()
      if (!err) {
        this.guessList = json
        this.getGuessListCompensate()
      }
    },
    // 商品池栏目点击
    onBlockClick (id) {
      if (id === 1) {
        this.$router.push('/fpHome/channel/list')
      } else if (id === 2) {
        const goodsListId = envConfig.FP_HOME_PAGE_RECOMMEND_GOODS_ID
        this.$router.push({ path: `/goodslist/${goodsListId}`, query: { noSort: 'noSort' } })
      } else if (id === 3) {
        const goodsListId = envConfig.FP_HOME_PAGE_NEWER_GOODS_ID
        this.$router.push({ path: `/goodslist/${goodsListId}`, query: { noSort: 'noSort' } })
      }
    },
    // banner 点击
    onBannerClick (item) {
      jumpHandler(item.url)
    },
    // 导航icon 点击
    onIconNavClick (item) {
      jumpHandler(item.url)
    },
    // 商品点击
    onGoodsClick (goods) {
      this.$router.push(`/goodsdetail/${goods.id}`)
    },
    // 一键加入购物车
    async onAddCartClick (goods) {
      if (!this.isLogin) {
        this.$store.dispatch('user/login')
        return
      }
      const goodsId = goods.id
      const addressInfo = JSON.stringify(this.curAddrInfo)
      this.$toast.queueLoading()
      const [res, json] = await getGoodsDetail({ bizCode: getBizCode('GOODS'), goodsId, code: '', addressInfo })
      this.$toast.queueClear()
      if (res.code === '0000' && res.code !== '8888') {
        const sku = json.skuList[0]
        this.$toast.queueLoading()
        const { flag } = await this.$store.dispatch('newCart/checkStock', { sku, skuNum: 1, address: this.curAddrInfo })
        this.$toast.queueClear()
        if (flag) {
          // 有库存
          await this.$store.dispatch('user/queryDefaultAddr')
          const info = this.curAddrInfo
          const addressInfo = JSON.stringify({
            provinceId: info.provinceId,
            cityId: info.cityId,
            countyId: info.countyId,
            townId: info.townId
          })
          this.$toast.queueLoading()
          const err = await this.$store.dispatch('newCart/add', { goodsId, skuId: sku.skuId, goodsNum: 1, addressInfo })
          this.$toast.queueLoading()
          if (err) {
            // 添加购物车失败
            this.$toast(err.msg)
          } else {
            // 添加购物车成功
            this.$toast('添加购物车成功')
          }
        } else {
          // 无库存
          this.$toast('商品库存不足')
        }
      } else {
        // 查询商品详情失败
        this.$toast('添加购物车失败')
      }
    },
    // 金额分割
    splitAmt,
    // 分转元
    fenToYuan,
    // 商品规格
    specs (goods) {
      const sku = goods.skuList[0] ? goods.skuList[0] : {}
      return (sku.param ? ' ' + sku.param : '') +
        (sku.param1 ? ' ' + sku.param1 : '') +
        (sku.param2 ? ' ' + sku.param2 : '') +
        (sku.param3 ? ' ' + sku.param3 : '') +
        (sku.param4 ? ' ' + sku.param4 : '')
    },
    newerScrollEvent (e) {
      this.newerScrollLeft = e.target.scrollLeft
    }
  },
  created () {
    this.setCol()
    this.setGutterWidth()
    this.setItemWidth()
    this.determineScreenSize() // 初始化时检查屏幕大小
    window.addEventListener('resize', this.onResize)
    this.getHeaderBannerList()
    this.getLimitedBannerList()
    this.getIconList()
    this.getLimitedList()
    this.getRecommendList()
    this.getNewerList()
    this.getGuessList()
    this.getLimitedListCompensate()
    this.getGuessListCompensate()
  },
  unmounted () {
    window.removeEventListener('resize', this.onResize)
    this.$toast.clear()
    if (this.$refs.newerScrollRef) this.$refs.newerScrollRef.removeEventListener('scroll', this.newerScrollEvent)
  },
  activated () {
    this.setCol()
    this.setGutterWidth()
    this.setItemWidth()
    this.determineScreenSize() // 初始化时检查屏幕大小
    this.getLimitedListCompensate()
    this.getGuessListCompensate()
    window.addEventListener('resize', this.onResize)
    // 配置滚动条位置
    if (this.$refs.newerScrollRef) this.$refs.newerScrollRef.scrollLeft = this.newerScrollLeft
  },
  deactivated () {
    window.removeEventListener('resize', this.onResize)
    this.$toast.clear()
  }
}
</script>
<style lang="less" scoped>
@screen-small: 320px;
@screen-medium: 540px;
//安卓8以下单独处理
.android_8 {
  .fp-page {
    padding-bottom: 49Px;
  }
}

.fp-page {
  padding-bottom: calc(49Px + var(--saib));
  height: 100%;
  overflow-y: scroll;

  .wrap {
    padding: 0 17Px;
  }
  .header {
    padding:0 15Px;
  }
  .swiper {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    @media (max-width: @screen-small) {
      img {
        max-width: 100%;
        height: auto;
        cursor: pointer;
      }
    }
    // 标准屏设备
    @media (min-width: @screen-small + 1) and (max-width: @screen-medium) {
      img {
        max-width: 100%;
        height: auto;
        cursor: pointer;
      }
    }
    &__indicator {
      z-index: 30;
      position: absolute;
      right: 5px;
      bottom: 7px;
      padding: 2px 5px;
      color: #fff;
      font-size: 16px;
      background: rgba(0, 0, 0, 0.1);
    }
  }

  .icon-nav-container {
    margin-top: 6Px;
    margin-bottom: 6Px;
    @media (min-width: @screen-medium + 1) {
      margin-top: 10Px;
      margin-bottom: 10Px;
    }
    .icon-nav {
      display: flex;
      justify-content: space-between;
      padding: 0 20Px;
      @media (min-width: @screen-medium + 1) {
        min-height: 80Px;
      }
    }

    .icon-item {
      text-align: center;
      @media (min-width: @screen-medium + 1) {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content:space-between;
      }
      .icon-item-img {
        width: 68px;
        height: 68px;
        @media (min-width: @screen-medium + 1) {
          width: 45Px;
          height: 45Px;
        }
      }

      .icon-item-text {
        font-family: OPPOSans-R;
        font-size: 26px;
        color: #333333;
        @media (min-width: @screen-medium + 1) {
          font-size: 14Px;
        }
      }
    }
  }

  .limited-swiper {
    position: relative;

    img {
      padding: 0 17Px;
      width: 100%;
      vertical-align: bottom;
    }

    .limited-swiper-pagination {
      position: absolute;
      z-index: 2;
      line-height: 1Px;
      text-align: center;
      pointer-events: none !important;
    }
  }

  .limited-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 7Px;
  }

  .recommend {
    .recommend-title {
      display: flex;
      height: 35Px;
      overflow: hidden;
      background-image: linear-gradient(256deg, #FFCE00 1%, #FF9C0A 100%);
      box-shadow: inset 0 2Px 6Px 0 rgba(255, 232, 143, 0.90);
      border-radius: 5Px 5Px 0 0;

      .recommend-title-inner {
        height: 100%;

        .recommend-text {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding: 0 10Px 0 20Px;
          height: 100%;
          background-image: linear-gradient(256deg, #FF8500 1%, #FF470A 100%);
          box-shadow: 1Px 0 6Px 0 rgba(255, 132, 0, 0.80), inset 0 1Px 6Px 0 rgba(255, 151, 84, 0.90);
          border-radius: 0 20Px 20Px 0;

          .recommend-text1 {
            margin-right: 5Px;
            width: 20Px;
            height: 20Px;
            background-image: url(assets/surprise.png);
            background-size: 100%;
            background-repeat: no-repeat;
          }

          .recommend-text2 {
            font-size: 14Px;
            font-weight: 500;
            color: #fff;
          }

          .recommend-text3 {
            margin-left: 8Px;
            font-size: 12Px;
            font-weight: 300;
            color: #fff;
          }
        }
      }
    }

    .recommend-list {
      position:relative;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 10Px 15Px;
      background-image: linear-gradient(180deg, #FFDDD4 15%, #FFF7F5 100%);
      box-shadow: 0 3Px 10Px 0 rgba(230, 230, 241, 0.40);
      border-radius: 0 0 5Px 5Px;
    }

    .left {
      flex-shrink: 0;
      margin-right: 36Px;
      width: 32%;
    }

    .right {
      flex: 1;
      height:100%;
    }

    .goods {
      height: 100%;
      img {
        width: 100%;
        border-radius: 5Px;
      }
    }

    .goodsmax {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      margin-bottom: 10Px;
      padding: 10Px;
      width: 100%;
      background-color: #fff;
      &:last-child {
        margin-bottom: 0
      }

      .content {
        flex-grow: 1;
      }

      img {
        width: 50Px;
      }
    }

    .text {
      .title {
        margin-bottom: 30px;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left;
        font-weight: normal;
        white-space: normal;
        font-size: 26px;
        line-height: 1.6;
        color: #171E24;
        word-break: break-all;
        @media (min-width: @screen-medium + 1) {
          font-size: 16Px;
          border-radius: 5Px;
          width: 100%;
          margin: 0 0 15Px 0;
        }
      }

      .desc {
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        padding: 8px 0 8px 0 ;
        text-align: left;
        font-weight: normal;
        white-space: normal;
        font-size: 18px;
        line-height: 24px;
        color: #879099;
      }
    }

    .amt {
      display: flex;
      align-items: center;
      line-height: 12Px;

      .amt1 {
        font-size: 20px;
        color: #F43535;
        @media (min-width: @screen-medium + 1) {
          font-size: 15Px;
        }
      }

      .amt2 {
        margin-left: 5Px;
        font-size: 18px;
        color: #B1BEC9;
        text-decoration: line-through;
        transform: scale(0.91);
        @media (min-width: @screen-medium + 1) {
          font-size: 15Px;
        }
      }

      .big {
        font-size: 24px;
        font-weight: bold;
        @media (min-width: @screen-medium + 1) {
          font-size: 17Px;
        }
      }
    }
  }

  .newer-list {
    overflow-x: scroll;
    overflow-y: hidden;

    &::-webkit-scrollbar {
      display: none;
    }

    .newer-list-inner {
      display: flex;
      box-sizing: content-box;
    }
  }

  .guess-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 17Px 17Px 0 17Px;
  }
}
</style>
